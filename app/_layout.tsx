import "../global.css";

import { Stack } from "expo-router";
import { useEffect } from "react";

import { initializeStripe } from "@/config/stripe";
import { colors } from "@/constants/colors";
import "@/lib/i18n"; // Initialize i18n
import { useColorScheme } from "@/lib/useColorScheme";

export default function AppLayout() {
	const { actualColorScheme } = useColorScheme();

	useEffect(() => {
		// Initialize Stripe
		initializeStripe();
	}, []);

	return (
		// <AuthProvider>
		<Stack screenOptions={{ headerShown: false, gestureEnabled: false }}>
			<Stack.Screen name="(protected)" />
			<Stack.Screen name="welcome" />
			<Stack.Screen
				name="sign-up"
				options={{
					presentation: "modal",
					headerShown: true,
					headerTitle: "Sign Up",
					headerStyle: {
						backgroundColor:
							actualColorScheme === "dark"
								? colors.dark.background
								: colors.light.background,
					},
					headerTintColor:
						actualColorScheme === "dark"
							? colors.dark.foreground
							: colors.light.foreground,
					gestureEnabled: true,
				}}
			/>
			<Stack.Screen
				name="sign-in"
				options={{
					presentation: "modal",
					headerShown: true,
					headerTitle: "Sign In",
					headerStyle: {
						backgroundColor:
							actualColorScheme === "dark"
								? colors.dark.background
								: colors.light.background,
					},
					headerTintColor:
						actualColorScheme === "dark"
							? colors.dark.foreground
							: colors.light.foreground,
					gestureEnabled: true,
				}}
			/>
			<Stack.Screen
				name="forgot-password"
				options={{
					presentation: "modal",
					headerShown: true,
					headerTitle: "Reset Password",
					headerStyle: {
						backgroundColor:
							actualColorScheme === "dark"
								? colors.dark.background
								: colors.light.background,
					},
					headerTintColor:
						actualColorScheme === "dark"
							? colors.dark.foreground
							: colors.light.foreground,
					gestureEnabled: true,
				}}
			/>
			<Stack.Screen
				name="reset-password"
				options={{
					presentation: "modal",
					headerShown: true,
					headerTitle: "Set New Password",
					headerStyle: {
						backgroundColor:
							actualColorScheme === "dark"
								? colors.dark.background
								: colors.light.background,
					},
					headerTintColor:
						actualColorScheme === "dark"
							? colors.dark.foreground
							: colors.light.foreground,
					gestureEnabled: false,
				}}
			/>
			<Stack.Screen
				name="auth/callback"
				options={{
					headerShown: false,
					gestureEnabled: false,
				}}
			/>
		</Stack>
		// </AuthProvider>
	);
}
